{"root": {"type": "decision", "feature": {"name": "amount", "type": "numeric", "column_number": 3, "values": [], "numeric_range": {"min": 45.99, "max": 320.75}, "min": 45.99, "max": 320.75}, "threshold": 56.5, "left": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "transaction_time", "type": "categorical", "column_number": 1, "values": [], "categorical_values": ["10:25:10Z", "08:45:55Z", "14:50:55Z", "12:25:55Z", "08:15:30Z", "14:10:30Z", "15:15:50Z", "10:40:15Z", "16:35:45Z", "09:25:40Z", "08:55:30Z", "10:15:50Z", "11:10:40Z", "14:45:30Z", "17:20:15Z", "08:20:35Z", "09:55:25Z", "10:35:20Z", "11:50:35Z", "12:55:20Z", "17:10:40Z", "09:45:20Z", "15:25:30Z", "15:40:35Z", "12:30:50Z", "11:15:40Z", "16:25:45Z", "16:05:30Z", "11:35:25Z", "16:35:25Z", "10:50:45Z", "13:50:20Z", "09:20:10Z", "13:45:25Z", "14:45:50Z", "09:30:00Z", "11:20:15Z", "08:30:45Z", "13:20:45Z"]}, "threshold": 0, "categories": {"08:15:30Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "08:20:35Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "08:30:45Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "08:45:55Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "08:55:30Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "09:20:10Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "09:25:40Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "09:30:00Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "09:45:20Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "09:55:25Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "10:15:50Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "10:25:10Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "10:35:20Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "10:40:15Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "10:50:45Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "11:10:40Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "11:15:40Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "11:20:15Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "11:35:25Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "11:50:35Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "12:25:55Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "12:30:50Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "12:55:20Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "13:20:45Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "14:10:30Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "14:45:30Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "14:45:50Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "14:50:55Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "15:15:50Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "15:25:30Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "15:40:35Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "16:05:30Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "16:25:45Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "16:35:25Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "16:35:45Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "17:10:40Z": {"type": "leaf", "threshold": 0, "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "17:20:15Z": {"type": "leaf", "threshold": 0, "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1, "impurity": 0}}, "class_distribution": {"no": 11, "yes": 27}, "samples": 38, "confidence": 0.7105263157894737, "impurity": 0.8680403986166403}, "class_distribution": {"no": 13, "yes": 27}, "samples": 40, "confidence": 0.675, "impurity": 0.9097361225311661}, "features": {"amount": {"name": "amount", "type": "numeric", "column_number": 3, "values": []}, "category": {"name": "category", "type": "categorical", "column_number": 4, "values": []}, "customer_signup_date": {"name": "customer_signup_date", "type": "categorical", "column_number": 2, "values": []}, "transaction_date": {"name": "transaction_date", "type": "categorical", "column_number": 0, "values": []}, "transaction_time": {"name": "transaction_time", "type": "categorical", "column_number": 1, "values": []}}, "features_by_index": [{"name": "transaction_date", "type": "categorical", "column_number": 0, "values": []}, {"name": "transaction_time", "type": "categorical", "column_number": 1, "values": []}, {"name": "customer_signup_date", "type": "categorical", "column_number": 2, "values": []}, {"name": "amount", "type": "numeric", "column_number": 3, "values": []}, {"name": "category", "type": "categorical", "column_number": 4, "values": []}], "target_type": "categorical", "target_column": "approved", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 40, "leaf_count": 38, "depth": 3}