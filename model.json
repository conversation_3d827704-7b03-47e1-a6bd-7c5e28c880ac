{"root": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 20268.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": [], "numeric_range": {"min": 106570, "max": 999187}, "min": 106570, "max": 999187}, "threshold": 998927.5, "left": {"type": "decision", "feature": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": 0, "max": 1}, "max": 1}, "threshold": 0.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 75017.5, "left": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 55}, "samples": 55, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 40}, "samples": 40, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 25}, "samples": 25, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 23}, "samples": 23, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 65, "Yes": 47}, "samples": 112, "confidence": 0.5803571428571429, "impurity": 0.9812872088817246}, "class_distribution": {"No": 120, "Yes": 47}, "samples": 167, "confidence": 0.718562874251497, "impurity": 0.8574016128221289}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 20}, "samples": 20, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 15}, "samples": 15, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 16}, "samples": 16, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 44, "Yes": 31}, "samples": 75, "confidence": 0.5866666666666667, "impurity": 0.9782176659354248}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 131}, "samples": 131, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 44, "Yes": 162}, "samples": 206, "confidence": 0.7864077669902912, "impurity": 0.7482932859824889}, "class_distribution": {"No": 164, "Yes": 209}, "samples": 373, "confidence": 0.5603217158176944, "impurity": 0.9894752857719396}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 72163.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 12}, "samples": 12, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 16}, "samples": 16, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 14}, "samples": 14, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 28, "Yes": 24}, "samples": 52, "confidence": 0.5384615384615384, "impurity": 0.9957274520849255}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 83}, "samples": 83, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 107}, "samples": 135, "confidence": 0.7925925925925926, "impurity": 0.7364977795505668}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 286}, "samples": 286, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 393}, "samples": 421, "confidence": 0.9334916864608076, "impurity": 0.3527561375432482}, "class_distribution": {"No": 192, "Yes": 602}, "samples": 794, "confidence": 0.7581863979848866, "impurity": 0.7980432506092013}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 194, "Yes": 602}, "samples": 796, "confidence": 0.7562814070351759, "impurity": 0.8011696748229913}, "class_distribution": {"No": 198, "Yes": 602}, "samples": 800, "confidence": 0.7525, "impurity": 0.8072916195433455}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "city": {"name": "city", "type": "categorical", "column_number": 10, "values": []}, "color": {"name": "color", "type": "categorical", "column_number": 8, "values": []}, "country": {"name": "country", "type": "categorical", "column_number": 11, "values": []}, "customer_id": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, "department": {"name": "department", "type": "categorical", "column_number": 9, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 18, "values": []}, "employment_status": {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, "gender": {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, "has_subscription": {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, "height_cm": {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, "income": {"name": "income", "type": "numeric", "column_number": 1, "values": []}, "income_bracket": {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, "is_premium": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, "last_login": {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, "owns_car": {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, "performance_grade": {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, "preferred_time": {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}, "price_dollars": {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, "satisfaction_rating": {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, "score_0_1": {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, "score_0_100": {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, "shirt_size": {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, "signup_date": {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, "weight_kg": {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, "zip_code": {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "income", "type": "numeric", "column_number": 1, "values": []}, {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, {"name": "color", "type": "categorical", "column_number": 8, "values": []}, {"name": "department", "type": "categorical", "column_number": 9, "values": []}, {"name": "city", "type": "categorical", "column_number": 10, "values": []}, {"name": "country", "type": "categorical", "column_number": 11, "values": []}, {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}, {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, {"name": "education", "type": "categorical", "column_number": 18, "values": []}, {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 29, "leaf_count": 18, "depth": 7}