# Feature configuration for datetime testing
# This YAML file defines the feature types and constraints for the datetime test dataset

features:
  transaction_date:
    type: datetime
    description: "Date when the transaction occurred"
    format: "date"
    constraints:
      min_date: "2023-01-01"
      max_date: "2023-12-31"
    
  transaction_time:
    type: datetime
    description: "Time when the transaction occurred (UTC)"
    format: "time"
    constraints:
      timezone: "UTC"
    
  customer_signup_date:
    type: datetime
    description: "Date when customer signed up for the service"
    format: "date"
    constraints:
      min_date: "2022-01-01"
      max_date: "2023-12-31"
    
  amount:
    type: numeric
    description: "Transaction amount in USD"
    constraints:
      min_value: 0.01
      max_value: 10000.00
      precision: 2
    
  category:
    type: categorical
    description: "Transaction category"
    values:
      - "retail"
      - "online"
      - "mobile"
      - "in-store"
    
  approved:
    type: categorical
    description: "Whether the transaction was approved"
    values:
      - "yes"
      - "no"

# Dataset configuration
dataset:
  target_column: "approved"
  has_header: true
  delimiter: ","
  
# Training configuration
training:
  algorithm: "c45"
  max_depth: 10
  min_samples_split: 2
  min_samples_leaf: 1
  
# Datetime conversion settings
datetime_conversion:
  enabled: true
  auto_detect: false
  strict_validation: true
  utc_normalization: true
  
# Feature creation settings
feature_creation:
  handle_missing_values: true
  missing_value_strategy: "skip"
  categorical_encoding: "label"
  numeric_scaling: false
