# Complex datetime feature configuration
# Tests multiple datetime formats and edge cases

features:
  event_datetime:
    type: datetime
    description: "Full datetime with milliseconds and timezone"
    format: "datetime"
    constraints:
      timezone: "UTC"
      precision: "milliseconds"
      min_datetime: "2023-01-01T00:00:00Z"
      max_datetime: "2023-12-31T23:59:59Z"
    
  start_date:
    type: datetime
    description: "Event start date (date only)"
    format: "date"
    constraints:
      min_date: "2023-01-01"
      max_date: "2023-12-31"
    
  end_time:
    type: datetime
    description: "Event end time (time only with UTC)"
    format: "time"
    constraints:
      timezone: "UTC"
      min_time: "00:00:00Z"
      max_time: "23:59:59Z"
    
  duration_minutes:
    type: numeric
    description: "Event duration in minutes"
    constraints:
      min_value: 1
      max_value: 1440  # 24 hours
      data_type: "integer"
    
  event_type:
    type: categorical
    description: "Type of event"
    values:
      - "meeting"
      - "workshop"
      - "training"
      - "conference"
      - "seminar"
    
  location:
    type: categorical
    description: "Event location type"
    values:
      - "office"
      - "remote"
      - "hybrid"
      - "external"
    
  success:
    type: categorical
    description: "Whether the event was successful"
    values:
      - "yes"
      - "no"

# Dataset configuration
dataset:
  target_column: "success"
  has_header: true
  delimiter: ","
  encoding: "utf-8"
  
# Advanced training configuration
training:
  algorithm: "c45"
  max_depth: 15
  min_samples_split: 3
  min_samples_leaf: 2
  max_features: null
  random_state: 42
  
# Advanced datetime conversion settings
datetime_conversion:
  enabled: true
  auto_detect: false
  strict_validation: true
  utc_normalization: true
  handle_milliseconds: true
  handle_timezones: true
  fallback_strategy: "error"
  
# Advanced feature creation settings
feature_creation:
  handle_missing_values: true
  missing_value_strategy: "skip"
  categorical_encoding: "label"
  numeric_scaling: false
  datetime_features:
    extract_components: false
    normalize_to_utc: true
    integer_conversion: true
    
# Validation settings
validation:
  cross_validation: true
  cv_folds: 5
  test_size: 0.2
  stratify: true
  
# Output settings
output:
  format: "json"
  include_probabilities: true
  include_feature_importance: true
  human_readable_thresholds: true
