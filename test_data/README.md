# DateTime Test Datasets

This directory contains comprehensive test datasets for validating the datetime implementation and code review changes.

## 📁 Dataset Files

### Basic DateTime Testing
- **`datetime_training.csv`** - Basic training dataset with date, time, and datetime columns
- **`datetime_prediction.csv`** - Prediction dataset matching the training format
- **`datetime_features.yaml`** - Feature configuration for basic datetime testing

### Complex DateTime Testing  
- **`complex_datetime_training.csv`** - Advanced dataset with milliseconds and mixed formats
- **`complex_datetime_features.yaml`** - Configuration for complex datetime scenarios

### Timezone Testing
- **`timezone_datetime_training.csv`** - Dataset with various timezone offsets
- **`timezone_datetime_features.yaml`** - Configuration for timezone-aware processing

## 🎯 Test Scenarios Covered

### 1. Basic DateTime Features (`datetime_*`)
- **Date-only values**: `2023-01-15` → `20230115`
- **Time-only values**: `09:30:00Z` → `93000`  
- **Full datetime**: `2023-01-15T09:30:00Z` → `20230115093000`
- **Mixed feature types**: datetime, numeric, categorical
- **Target prediction**: Binary classification (approved: yes/no)

### 2. Complex DateTime Features (`complex_datetime_*`)
- **High-precision datetime**: `2023-03-15T09:30:45.123Z` (milliseconds)
- **Mixed datetime formats**: Full datetime, date-only, time-only in same dataset
- **Integer duration**: Numeric feature alongside datetime features
- **Multi-class categorical**: event_type, location
- **Target prediction**: Binary classification (success: yes/no)

### 3. Timezone DateTime Features (`timezone_datetime_*`)
- **Timezone offsets**: `-04:00`, `+01:00`, `+09:00`, etc.
- **UTC normalization**: All timezones converted to UTC integers
- **Global coverage**: EST, PST, GMT, JST, IST, AEST, etc.
- **Regional categorization**: Geographic regions as categorical features
- **Target prediction**: Binary classification (fulfilled: yes/no)

## 🔧 Usage Examples

### Training with Basic Dataset
```bash
# Train model with basic datetime features
./mulberri train \
  --data test_data/datetime_training.csv \
  --features test_data/datetime_features.yaml \
  --output model.json

# Make predictions
./mulberri predict \
  --model model.json \
  --data test_data/datetime_prediction.csv \
  --output predictions.csv
```

### Training with Complex Dataset
```bash
# Train with complex datetime features (milliseconds, mixed formats)
./mulberri train \
  --data test_data/complex_datetime_training.csv \
  --features test_data/complex_datetime_features.yaml \
  --output complex_model.json
```

### Training with Timezone Dataset
```bash
# Train with timezone-aware datetime features
./mulberri train \
  --data test_data/timezone_datetime_training.csv \
  --features test_data/timezone_datetime_features.yaml \
  --output timezone_model.json
```

## ✅ Validation Points

### Code Review Implementation
- **✅ No floating-point precision loss**: All datetime values converted to int64
- **✅ Immediate validation**: Strict format validation without type guessing
- **✅ UTC normalization**: All timezone offsets converted to UTC
- **✅ Integer arithmetic**: Decision tree splitting uses int64 operations
- **✅ Three-type system**: string (categorical), float64 (numeric), int64 (datetime)

### Expected Integer Conversions
```
Date format:     2023-01-15 → 20230115
Time format:     09:30:00Z → 93000  
DateTime format: 2023-01-15T09:30:00Z → 20230115093000
Timezone conv:   2023-01-15T09:30:00-05:00 → 20230115143000 (UTC)
```

### Feature Types Validation
- **DateTime features**: Converted to int64 during dataset loading
- **Numeric features**: Remain as float64
- **Categorical features**: Remain as strings
- **Target column**: Categorical string for classification

## 🧪 Test Commands

### Run DateTime Converter Tests
```bash
go test ./internals/utils/datetime_converter -v
go test ./internals/utils/datetime_converter -cover
```

### Run Training Tests
```bash
go test ./internals/training -v -run TestDateTimeFeatureSplitting
```

### Run Integration Tests
```bash
# Test with actual datasets
go test ./cmd/mulberri -v -run TestTrainingWithDateTimeFeatures
```

## 📊 Expected Results

### Training Output
- Model should train successfully with all datetime formats
- Decision tree should use integer thresholds for datetime features
- Serialized model should show human-readable datetime thresholds

### Prediction Output
- Predictions should be consistent across timezone variations
- Integer arithmetic should maintain precision
- Performance should be optimal with pre-converted datetime values

This test suite comprehensively validates the datetime implementation changes from the code review!
