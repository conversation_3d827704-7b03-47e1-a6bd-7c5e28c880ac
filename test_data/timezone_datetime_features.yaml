# Timezone-aware datetime feature configuration
# Tests UTC normalization and timezone handling

features:
  order_datetime:
    type: datetime
    description: "Order timestamp with timezone offset"
    format: "datetime"
    constraints:
      timezone_aware: true
      normalize_to_utc: true
      min_datetime: "2023-01-01T00:00:00Z"
      max_datetime: "2023-12-31T23:59:59Z"
    
  delivery_date:
    type: datetime
    description: "Scheduled delivery date"
    format: "date"
    constraints:
      min_date: "2023-01-01"
      max_date: "2024-12-31"
    
  pickup_time:
    type: datetime
    description: "Pickup time with timezone"
    format: "time"
    constraints:
      timezone_aware: true
      normalize_to_utc: true
    
  customer_timezone:
    type: categorical
    description: "Customer's timezone"
    values:
      - "EST"
      - "PST"
      - "CST"
      - "MST"
      - "GMT"
      - "CET"
      - "JST"
      - "IST"
      - "AEST"
      - "UTC"
      - "BRT"
      - "BRST"
      - "ICT"
      - "EAT"
      - "AKST"
      - "BST"
      - "GST"
      - "HST"
    
  order_value:
    type: numeric
    description: "Order value in USD"
    constraints:
      min_value: 0.01
      max_value: 50000.00
      precision: 2
    
  region:
    type: categorical
    description: "Geographic region"
    values:
      - "north_america"
      - "south_america"
      - "europe"
      - "asia"
      - "africa"
      - "oceania"
    
  fulfilled:
    type: categorical
    description: "Whether order was fulfilled"
    values:
      - "yes"
      - "no"

# Dataset configuration
dataset:
  target_column: "fulfilled"
  has_header: true
  delimiter: ","
  
# Timezone-specific training configuration
training:
  algorithm: "c45"
  max_depth: 12
  min_samples_split: 2
  min_samples_leaf: 1
  handle_timezone_features: true
  
# Timezone conversion settings
datetime_conversion:
  enabled: true
  auto_detect: false
  strict_validation: true
  utc_normalization: true
  timezone_handling: "convert_to_utc"
  preserve_timezone_info: false
  handle_dst: true
  
# Feature creation with timezone awareness
feature_creation:
  handle_missing_values: true
  missing_value_strategy: "skip"
  categorical_encoding: "label"
  numeric_scaling: false
  timezone_features:
    normalize_all_to_utc: true
    extract_timezone_offset: false
    handle_ambiguous_times: "error"
    
# Validation for timezone data
validation:
  timezone_consistency: true
  utc_conversion_accuracy: true
  cross_timezone_validation: true
